# Test script for concurrent connections
Write-Host "Testing concurrent connections to HTTP server..."

# Function to make a single HTTP request
function Test-HttpRequest {
    param($path, $id)
    
    try {
        $client = New-Object System.Net.Sockets.TcpClient
        $client.Connect('127.0.0.1', 4221)
        $stream = $client.GetStream()
        
        $request = "GET $path HTTP/1.1`r`nHost: localhost`r`nUser-Agent: TestClient$id`r`n`r`n"
        $requestBytes = [System.Text.Encoding]::ASCII.GetBytes($request)
        $stream.Write($requestBytes, 0, $requestBytes.Length)
        
        # Read response
        $buffer = New-Object byte[] 1024
        $bytesRead = $stream.Read($buffer, 0, 1024)
        $response = [System.Text.Encoding]::ASCII.GetString($buffer, 0, $bytesRead)
        
        $client.Close()
        
        $statusLine = $response.Split("`r`n")[0]
        Write-Host "Request $id to $path completed: $statusLine"
        return $statusLine
    }
    catch {
        Write-Host "Request $id failed: $($_.Exception.Message)"
        return "FAILED"
    }
}

# Start multiple concurrent requests
$jobs = @()
$jobs += Start-Job -ScriptBlock ${function:Test-HttpRequest} -ArgumentList "/", 1
$jobs += Start-Job -ScriptBlock ${function:Test-HttpRequest} -ArgumentList "/echo/concurrent", 2
$jobs += Start-Job -ScriptBlock ${function:Test-HttpRequest} -ArgumentList "/user-agent", 3

# Wait for all jobs to complete
$jobs | Wait-Job | Out-Null

# Get results
$results = $jobs | Receive-Job
$jobs | Remove-Job

Write-Host "`nResults:"
$results | ForEach-Object { Write-Host "  $_" }

Write-Host "`nConcurrent connection test completed."
