using System;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

class TestClient
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("Testing concurrent connections...");
        
        // Start multiple concurrent requests
        var tasks = new Task[]
        {
            MakeRequest("/", 1),
            MakeRequest("/echo/concurrent", 2),
            MakeRequest("/user-agent", 3)
        };
        
        await Task.WhenAll(tasks);
        Console.WriteLine("All requests completed.");
    }
    
    static async Task MakeRequest(string path, int id)
    {
        try
        {
            using var client = new TcpClient();
            await client.ConnectAsync("127.0.0.1", 4221);
            
            using var stream = client.GetStream();
            
            string request = $"GET {path} HTTP/1.1\r\nHost: localhost\r\nUser-Agent: TestClient{id}\r\n\r\n";
            byte[] requestBytes = Encoding.ASCII.GetBytes(request);
            await stream.WriteAsync(requestBytes, 0, requestBytes.Length);
            
            byte[] buffer = new byte[1024];
            int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
            string response = Encoding.ASCII.GetString(buffer, 0, bytesRead);
            
            string statusLine = response.Split("\r\n")[0];
            Console.WriteLine($"Request {id} to {path}: {statusLine}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Request {id} failed: {ex.Message}");
        }
    }
}
